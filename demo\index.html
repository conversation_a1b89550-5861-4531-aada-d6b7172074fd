<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Personal Portfolio - Demo</title>
    <link rel="stylesheet" href="css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
</head>
<body>
    <!-- 移动端警告 -->
    <div class="mobile-warning">
        <div class="mobile-warning-text">
            This webpage is better accessed through a computer browser.
        </div>
    </div>

    <!-- 自定义光标 -->
    <div class="custom-cursor"></div>

    <!-- 加载动画 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-container">
            <div class="loading-logo">
                <div class="loading-text">LOADING</div>
                <div class="loading-bar">
                    <div class="loading-progress" id="loading-progress"></div>
                </div>
                <div class="loading-percentage" id="loading-percentage">0%</div>
            </div>
        </div>
    </div>

    <!-- 相机取景框 -->
    <div class="camera-frame">
        <div class="frame-corner top-left"></div>
        <div class="frame-corner top-right"></div>
        <div class="frame-corner bottom-left"></div>
        <div class="frame-corner bottom-right"></div>
    </div>

    <!-- 主内容 -->
    <div class="main-content" id="main-content">
        <!-- 导航栏 -->
        <nav class="navbar">
            <div class="nav-logo">
                <div class="morphing-logo" id="morphing-logo">
                    <svg viewBox="0 0 200 100" class="logo-svg">
                        <path id="logo-path" d="M20,50 L180,50 M100,20 L100,80" stroke="#000" stroke-width="3" fill="none"/>
                    </svg>
                </div>
                <span class="logo-text">X OBERON</span>
            </div>
            <div class="nav-links">
                <a href="#page1" class="nav-link active">HOME</a>
                <a href="#page2" class="nav-link">ABOUT</a>
            </div>
        </nav>

        <!-- 右侧页面导航 -->
        <nav class="page-navigation">
            <div class="nav-dot active" data-page="HOME"></div>
            <div class="nav-dot" data-page="ABOUT"></div>
            <div class="nav-dot" data-page="IDENTITY"></div>
            <div class="nav-dot" data-page="CREATOR"></div>
            <div class="nav-dot" data-page="DEVELOPER"></div>
        </nav>

        <!-- 滚动提示 -->
        <div class="scroll-hint">
            <div class="scroll-arrow"></div>
            <div class="scroll-text">SCROLL</div>
        </div>

        <!-- 滚动容器 -->
        <div class="scroll-container" id="scroll-container">

        <!-- 第1页 - 主页内容 -->
        <section class="page-section hero-section active-section" id="page1">
            <div class="hero-background">
                <!-- 简化的X OBERON水印 -->
                <div class="watermark-text watermark-1">X OBERON</div>
                <div class="watermark-text watermark-2">X OBERON</div>
                <div class="watermark-text watermark-3">X OBERON</div>
            </div>
            
            <div class="hero-content">
                <div class="hero-title">
                    <h1 class="main-title">
                        <span class="title-line">HELLO</span>
                        <span class="title-line">I'M</span>
                        <span class="title-line highlight">X OBERON</span>
                    </h1>
                </div>
                
                <div class="hero-subtitle">
                    <p>Creating digital experiences with passion and precision</p>
                </div>
                
                <div class="hero-cta">
                    <button class="cta-button" onclick="scrollToSection('projects')">
                        VIEW MY WORK
                    </button>
                </div>
            </div>

            <div class="scroll-indicator">
                <div class="scroll-line"></div>
                <span class="scroll-text">SCROLL</span>
            </div>
        </section>

        <!-- 第2页 - 关于页面 -->
        <section class="page-section" id="page2">
            <!-- 贴纸特效装饰 -->
            <div class="sticker-decorations">
                <!-- 左上角贴纸组 -->
                <div class="sticker-group sticker-group-1">
                    <div class="sticker sticker-polaroid">
                        <div class="sticker-content">
                            <div class="sticker-image">XO</div>
                            <div class="sticker-label">CREATOR</div>
                        </div>
                    </div>
                    <div class="sticker sticker-tag">
                        <div class="sticker-text">DESIGN</div>
                    </div>
                </div>

                <!-- 右上角贴纸组 -->
                <div class="sticker-group sticker-group-2">
                    <div class="sticker sticker-polaroid sticker-rotated">
                        <div class="sticker-content">
                            <div class="sticker-image">DEV</div>
                            <div class="sticker-label">CODE</div>
                        </div>
                    </div>
                </div>

                <!-- 左下角贴纸组 -->
                <div class="sticker-group sticker-group-3">
                    <div class="sticker sticker-badge">
                        <div class="sticker-text">ARTIST</div>
                    </div>
                    <div class="sticker sticker-tag sticker-small">
                        <div class="sticker-text">2024</div>
                    </div>
                </div>

                <!-- 右下角贴纸组 -->
                <div class="sticker-group sticker-group-4">
                    <div class="sticker sticker-polaroid sticker-tilted">
                        <div class="sticker-content">
                            <div class="sticker-image">ART</div>
                            <div class="sticker-label">DIGITAL</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 简单装饰元素 -->
            <div class="page-decoration">
                <div class="deco-dot dot-1"></div>
                <div class="deco-dot dot-2"></div>
                <div class="deco-line line-1"></div>
            </div>

            <div class="about-content">
                <div class="about-title">X Oberon</div>
                <div class="about-description">
                    <p class="about-line">
                        <span class="normal">Creative developer specializing in</span>
                        <span class="highlight">Interactive Design</span>
                        <span class="highlight">Web Development</span>
                        <span class="normal">and</span>
                        <span class="highlight">Digital Art</span>
                    </p>
                    <div class="about-paragraph">
                        Passionate creator committed to pushing the boundaries of digital experiences.
                        Building immersive web applications that blend technology with artistry.
                        Devoted to innovation and aesthetic excellence.
                    </div>
                </div>
            </div>
        </section>

        <!-- 第3页 - 身份展示 -->
        <section class="page-section" id="page3">
            <!-- 简单装饰 -->
            <div class="page-decoration">
                <div class="deco-circle"></div>
                <div class="deco-square"></div>
            </div>

            <div class="identity-text-group">
                <div class="identity-title">A Creator</div>
                <div class="identity-title">A Developer</div>
                <div class="identity-title">A Designer</div>
            </div>
        </section>

        <!-- 第4页 - 创作者 -->
        <section class="page-section" id="page4">
            <!-- 撕纸效果 - 顶部 -->
            <div class="torn-paper-top">
                <!-- 主撕纸边缘 -->
                <svg viewBox="0 0 1200 60" class="torn-edge-svg">
                    <path d="M0,60 L0,25 Q30,8 60,18 Q90,28 120,15 Q150,5 180,20 Q210,30 240,18 Q270,8 300,22 Q330,32 360,20 Q390,10 420,25 Q450,35 480,22 Q510,12 540,28 Q570,38 600,25 Q630,15 660,30 Q690,40 720,28 Q750,18 780,32 Q810,42 840,30 Q870,20 900,35 Q930,45 960,32 Q990,22 1020,38 Q1050,48 1080,35 Q1110,25 1140,40 Q1170,50 1200,38 L1200,60 Z" fill="#fff"/>
                </svg>
                <!-- 撕纸阴影 -->
                <div class="torn-shadow-top"></div>
            </div>

            <!-- 撕纸效果 - 底部 -->
            <div class="torn-paper-bottom">
                <!-- 主撕纸边缘 -->
                <svg viewBox="0 0 1200 60" class="torn-edge-svg">
                    <path d="M0,0 L0,35 Q30,52 60,42 Q90,32 120,45 Q150,55 180,40 Q210,30 240,42 Q270,52 300,38 Q330,28 360,40 Q390,50 420,35 Q450,25 480,38 Q510,48 540,32 Q570,22 600,35 Q630,45 660,30 Q690,20 720,32 Q750,42 780,28 Q810,18 840,30 Q870,40 900,25 Q930,15 960,28 Q990,38 1020,22 Q1050,12 1080,25 Q1110,35 1140,20 Q1170,10 1200,22 L1200,0 Z" fill="#fff"/>
                </svg>
                <!-- 撕纸阴影 -->
                <div class="torn-shadow-bottom"></div>
            </div>

            <!-- 纸张纹理背景 -->
            <div class="paper-texture"></div>

            <!-- 简单装饰 -->
            <div class="page-decoration">
                <div class="floating-dot dot-1"></div>
                <div class="floating-dot dot-2"></div>
                <div class="floating-dot dot-3"></div>
            </div>

            <div class="page-content">
                <div class="whoami-title">A Creator</div>
                <div style="position: absolute; top: 28vh; width: 36vw; height: 0; left: 2vw; border: 1px solid #000;"></div>
                <div class="page-link" style="top: 28vh; left: 2.8vw; width: 35vw; height: 3vh; margin-top: 1vh;">
                    <div style="position: relative; float: left; color: #000; font-size: 1.8vh;">View My Work</div>
                    <div style="position: relative; float: right; color: #000; font-size: 2.5vh;">→</div>
                </div>
            </div>
            <div class="page-quote" style="position: absolute; right: 50vw; top: 52vh; color: #fff; font-size: 3.8vw; text-align: center; z-index: 1001;">
                <div>Imagination</div>
                <div>is</div>
                <div>Everything</div>
            </div>
        </section>

        <!-- 第5页 - 开发者 -->
        <section class="page-section" id="page5">
            <!-- 简单代码背景 -->
            <canvas id="code-canvas"></canvas>

            <!-- 简单装饰 -->
            <div class="page-decoration">
                <div class="code-symbol symbol-1">&lt;/&gt;</div>
                <div class="code-symbol symbol-2">{}</div>
                <div class="code-symbol symbol-3">[]</div>
            </div>

            <div class="page-content">
                <div class="whoami-title-right">A Developer</div>
                <div class="page-link-right">
                    <div>View My Code</div>
                    <div>→</div>
                </div>
            </div>
            <div class="page-quote-right">
                <div>Code</div>
                <div>is Poetry</div>
            </div>
        </section>
    </div>

    <script src="js/main.js"></script>
</body>
</html>
