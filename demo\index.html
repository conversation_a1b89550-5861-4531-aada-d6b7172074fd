<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Personal Portfolio - Demo</title>
    <link rel="stylesheet" href="css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
</head>
<body>
    <!-- 移动端警告 -->
    <div class="mobile-warning">
        <div class="mobile-warning-text">
            This webpage is better accessed through a computer browser.
        </div>
    </div>

    <!-- 自定义光标 - MacBook风格箭头 -->
    <div class="custom-cursor">
        <svg viewBox="0 0 24 24" class="cursor-svg">
            <!-- 阴影 -->
            <path d="M3 3 L3 17 L9 11 L13 11 L21 21 L19 19 L13 13 L9 13 L3 17 Z"
                  fill="rgba(0,0,0,0.2)" transform="translate(1,1)"/>
            <!-- 主体箭头 -->
            <path d="M3 3 L3 17 L9 11 L13 11 L21 21 L19 19 L13 13 L9 13 Z"
                  fill="#000" stroke="#fff" stroke-width="0.5"/>
            <!-- 内部高光 -->
            <path d="M4 4 L4 15 L8.5 10.5 L12 10.5 L19 19 L17.5 17.5 L12 12 L8.5 12 Z"
                  fill="#fff" opacity="0.3"/>
        </svg>
    </div>

    <!-- 加载动画 -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-container">
            <div class="loading-logo">
                <div class="loading-text">LOADING</div>
                <div class="loading-bar">
                    <div class="loading-progress" id="loading-progress"></div>
                </div>
                <div class="loading-percentage" id="loading-percentage">0%</div>
            </div>
        </div>
    </div>

    <!-- 相机取景框 -->
    <div class="camera-frame">
        <div class="frame-corner top-left"></div>
        <div class="frame-corner top-right"></div>
        <div class="frame-corner bottom-left"></div>
        <div class="frame-corner bottom-right"></div>
    </div>

    <!-- 主内容 -->
    <div class="main-content" id="main-content">
        <!-- 导航栏 -->
        <nav class="navbar">
            <div class="nav-logo">
                <div class="morphing-logo" id="morphing-logo">
                    <svg viewBox="0 0 200 100" class="logo-svg">
                        <path id="logo-path" d="M20,50 L180,50 M100,20 L100,80" stroke="#000" stroke-width="3" fill="none"/>
                    </svg>
                </div>
                <span class="logo-text">X OBERON</span>
            </div>
            <div class="nav-links">
                <a href="#page1" class="nav-link active">HOME</a>
                <a href="#page2" class="nav-link">ABOUT</a>
            </div>
        </nav>

        <!-- 右侧页面导航 -->
        <nav class="page-navigation">
            <div class="nav-dot active" data-page="HOME"></div>
            <div class="nav-dot" data-page="ABOUT"></div>
            <div class="nav-dot" data-page="IDENTITY"></div>
            <div class="nav-dot" data-page="CREATOR"></div>
            <div class="nav-dot" data-page="DEVELOPER"></div>
        </nav>

        <!-- 滚动提示 -->
        <div class="scroll-hint">
            <div class="scroll-arrow"></div>
            <div class="scroll-text">SCROLL</div>
        </div>

        <!-- 滚动容器 -->
        <div class="scroll-container" id="scroll-container">

        <!-- 第1页 - 主页内容 -->
        <section class="page-section hero-section active-section" id="page1">
            <!-- GitHub图标 -->
            <div class="github-icon">
                <a href="https://github.com" target="_blank" rel="noopener noreferrer">
                    <svg viewBox="0 0 24 24" class="github-svg">
                        <path d="M12 0C5.374 0 0 5.373 0 12 0 17.302 3.438 21.8 8.207 23.387c.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23A11.509 11.509 0 0112 5.803c1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576C20.566 21.797 24 17.3 24 12c0-6.627-5.373-12-12-12z"/>
                    </svg>
                </a>
            </div>

            <div class="hero-background">
                <!-- 简化的X OBERON水印 -->
                <div class="watermark-text watermark-1">X OBERON</div>
                <div class="watermark-text watermark-2">X OBERON</div>
                <div class="watermark-text watermark-3">X OBERON</div>
            </div>

            <div class="hero-content">
                <div class="hero-title">
                    <h1 class="main-title">
                        <span class="title-line">HELLO</span>
                        <span class="title-line">I'M</span>
                        <span class="title-line highlight">X OBERON</span>
                    </h1>
                </div>
                
                <div class="hero-subtitle">
                    <p>Creating digital experiences with passion and precision</p>
                </div>
                
                <div class="hero-cta">
                    <button class="cta-button" onclick="scrollToSection('projects')">
                        VIEW MY WORK
                    </button>
                </div>
            </div>

            <div class="scroll-indicator">
                <div class="scroll-line"></div>
                <span class="scroll-text">SCROLL</span>
            </div>
        </section>

        <!-- 第2页 - 关于页面 -->
        <section class="page-section" id="page2">
            <!-- 贴纸特效装饰 -->
            <div class="sticker-decorations">
                <!-- 左上角贴纸组 -->
                <div class="sticker-group sticker-group-1">
                    <div class="sticker sticker-polaroid">
                        <div class="sticker-content">
                            <div class="sticker-image">XO</div>
                            <div class="sticker-label">CREATOR</div>
                        </div>
                    </div>
                    <div class="sticker sticker-tag">
                        <div class="sticker-text">DESIGN</div>
                    </div>
                </div>

                <!-- 右上角贴纸组 -->
                <div class="sticker-group sticker-group-2">
                    <div class="sticker sticker-polaroid sticker-rotated">
                        <div class="sticker-content">
                            <div class="sticker-image">DEV</div>
                            <div class="sticker-label">CODE</div>
                        </div>
                    </div>
                </div>

                <!-- 左下角贴纸组 -->
                <div class="sticker-group sticker-group-3">
                    <div class="sticker sticker-badge">
                        <div class="sticker-text">ARTIST</div>
                    </div>
                    <div class="sticker sticker-tag sticker-small">
                        <div class="sticker-text">2024</div>
                    </div>
                </div>

                <!-- 右下角贴纸组 -->
                <div class="sticker-group sticker-group-4">
                    <div class="sticker sticker-polaroid sticker-tilted">
                        <div class="sticker-content">
                            <div class="sticker-image">ART</div>
                            <div class="sticker-label">DIGITAL</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 简单装饰元素 -->
            <div class="page-decoration">
                <div class="deco-dot dot-1"></div>
                <div class="deco-dot dot-2"></div>
                <div class="deco-line line-1"></div>
            </div>

            <div class="about-content">
                <div class="about-title">X Oberon</div>
                <div class="about-description">
                    <p class="about-line">
                        <span class="normal">Creative developer specializing in</span>
                        <span class="highlight">Interactive Design</span>
                        <span class="highlight">Web Development</span>
                        <span class="normal">and</span>
                        <span class="highlight">Digital Art</span>
                    </p>
                    <div class="about-paragraph">
                        Passionate creator committed to pushing the boundaries of digital experiences.
                        Building immersive web applications that blend technology with artistry.
                        Devoted to innovation and aesthetic excellence.
                    </div>
                </div>
            </div>
        </section>

        <!-- 第3页 - 身份展示 -->
        <section class="page-section" id="page3">
            <!-- 简单装饰 -->
            <div class="page-decoration">
                <div class="deco-circle"></div>
                <div class="deco-square"></div>
            </div>

            <div class="identity-text-group">
                <div class="identity-title">A Creator</div>
                <div class="identity-title">A Developer</div>
                <div class="identity-title">A Designer</div>
            </div>
        </section>

        <!-- 第4页 - 创作者 -->
        <section class="page-section" id="page4">
            <!-- 简单装饰 -->
            <div class="page-decoration">
                <div class="floating-dot dot-1"></div>
                <div class="floating-dot dot-2"></div>
                <div class="floating-dot dot-3"></div>
            </div>

            <div class="page-content">
                <div class="whoami-title">A Creator</div>
                <div style="position: absolute; top: 28vh; width: 36vw; height: 0; left: 2vw; border: 1px solid #000;"></div>
                <div class="page-link" style="top: 28vh; left: 2.8vw; width: 35vw; height: 3vh; margin-top: 1vh;">
                    <div style="position: relative; float: left; color: #000; font-size: 1.8vh;">View My Work</div>
                    <div style="position: relative; float: right; color: #000; font-size: 2.5vh;">→</div>
                </div>
            </div>
            <div class="page-quote" style="position: absolute; right: 50vw; top: 52vh; color: #fff; font-size: 3.8vw; text-align: center; z-index: 1001;">
                <div>Imagination</div>
                <div>is</div>
                <div>Everything</div>
            </div>
        </section>

        <!-- 第5页 - 开发者 -->
        <section class="page-section" id="page5">
            <!-- 简单代码背景 -->
            <canvas id="code-canvas"></canvas>

            <!-- 简单装饰 -->
            <div class="page-decoration">
                <div class="code-symbol symbol-1">&lt;/&gt;</div>
                <div class="code-symbol symbol-2">{}</div>
                <div class="code-symbol symbol-3">[]</div>
            </div>

            <div class="page-content">
                <div class="whoami-title-right">A Developer</div>
                <div class="page-link-right">
                    <div>View My Code</div>
                    <div>→</div>
                </div>
            </div>
            <div class="page-quote-right">
                <div>Code</div>
                <div>is Poetry</div>
            </div>
        </section>
    </div>

    <script src="js/main.js"></script>
</body>
</html>
