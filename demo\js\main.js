// ==================== 全局变量 ====================
let isLoading = true;
let loadingProgress = 0;

// ==================== DOM 加载完成后执行 ====================
document.addEventListener('DOMContentLoaded', function() {
    // 注册GSAP插件
    if (typeof gsap !== 'undefined' && typeof ScrollTrigger !== 'undefined') {
        gsap.registerPlugin(ScrollTrigger);
    }
    initializeApp();
});

// ==================== 初始化应用 ====================
function initializeApp() {
    // 初始化自定义光标
    initCustomCursor();
    
    // 开始加载动画
    startLoadingAnimation();

    // 初始化水印系统
    initWatermarkSystem();

    // 初始化滚动驱动动画
    initScrollDrivenAnimations();

    // 初始化页面特定功能
    if (document.getElementById('home')) {
        initHomePage();
    }

    if (document.getElementById('about')) {
        initAboutPage();
    }

    // 添加平滑滚动
    initSmoothScroll();
}

// ==================== 自定义光标 ====================
function initCustomCursor() {
    const cursor = document.querySelector('.custom-cursor');
    if (!cursor) return;
    
    let mouseX = 0;
    let mouseY = 0;
    let cursorX = 0;
    let cursorY = 0;
    
    // 跟踪鼠标位置
    document.addEventListener('mousemove', (e) => {
        mouseX = e.clientX;
        mouseY = e.clientY;
    });
    
    // 平滑移动光标
    function animateCursor() {
        const speed = 0.1;
        cursorX += (mouseX - cursorX) * speed;
        cursorY += (mouseY - cursorY) * speed;
        
        cursor.style.left = cursorX + 'px';
        cursor.style.top = cursorY + 'px';
        
        requestAnimationFrame(animateCursor);
    }
    
    animateCursor();
    
    // 悬停效果
    const interactiveElements = document.querySelectorAll('a, button, .project-card');
    interactiveElements.forEach(element => {
        element.addEventListener('mouseenter', () => {
            cursor.style.transform = 'scale(2)';
        });
        
        element.addEventListener('mouseleave', () => {
            cursor.style.transform = 'scale(1)';
        });
    });
}

// ==================== 加载动画 ====================
function startLoadingAnimation() {
    const loadingOverlay = document.getElementById('loading-overlay');
    const loadingProgressBar = document.getElementById('loading-progress');
    const loadingPercentage = document.getElementById('loading-percentage');
    
    if (!loadingOverlay) return;
    
    // 模拟加载进度
    const loadingInterval = setInterval(() => {
        loadingProgress += Math.random() * 15;
        
        if (loadingProgress >= 100) {
            loadingProgress = 100;
            clearInterval(loadingInterval);
            
            // 完成加载
            setTimeout(() => {
                finishLoading();
            }, 500);
        }
        
        // 更新进度条和百分比
        if (loadingProgressBar) {
            loadingProgressBar.style.width = loadingProgress + '%';
        }
        if (loadingPercentage) {
            loadingPercentage.textContent = Math.round(loadingProgress) + '%';
        }
    }, 100);
}

function finishLoading() {
    const loadingOverlay = document.getElementById('loading-overlay');
    const mainContent = document.getElementById('main-content');
    
    if (loadingOverlay) {
        loadingOverlay.classList.add('fade-out');
        setTimeout(() => {
            loadingOverlay.style.display = 'none';
        }, 500);
    }
    
    if (mainContent) {
        mainContent.style.display = 'block';
        // 添加入场动画
        setTimeout(() => {
            animatePageElements();
        }, 100);
    }
    
    isLoading = false;
}

// ==================== 页面元素动画 ====================
function animatePageElements() {
    // 标题动画
    const titleLines = document.querySelectorAll('.title-line');
    titleLines.forEach((line, index) => {
        line.style.opacity = '0';
        line.style.transform = 'translateY(50px)';
        
        setTimeout(() => {
            line.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
            line.style.opacity = '1';
            line.style.transform = 'translateY(0)';
        }, index * 200);
    });
    
    // 其他元素动画
    const animatedElements = document.querySelectorAll('.hero-subtitle, .hero-cta, .about-description');
    animatedElements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            element.style.transition = 'all 0.6s ease-out';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, (titleLines.length * 200) + (index * 100));
    });
}

// ==================== 主页功能 ====================
function initHomePage() {
    // 项目卡片悬停效果
    const projectCards = document.querySelectorAll('.project-card');
    projectCards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // 滚动指示器动画
    const scrollIndicator = document.querySelector('.scroll-indicator');
    if (scrollIndicator) {
        scrollIndicator.addEventListener('click', () => {
            scrollToSection('projects');
        });
    }
}

// ==================== 关于页面功能 ====================
function initAboutPage() {
    // 技能条动画
    const skillBars = document.querySelectorAll('.skill-progress');
    
    // 创建 Intersection Observer 来触发动画
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const progressBar = entry.target;
                const progress = progressBar.getAttribute('data-progress');
                
                setTimeout(() => {
                    progressBar.style.width = progress + '%';
                }, 200);
                
                observer.unobserve(progressBar);
            }
        });
    }, { threshold: 0.5 });
    
    skillBars.forEach(bar => {
        observer.observe(bar);
    });
    
    // 时间线动画
    const timelineItems = document.querySelectorAll('.timeline-item');
    const timelineObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, { threshold: 0.3 });
    
    timelineItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(50px)';
        item.style.transition = `all 0.6s ease-out ${index * 0.1}s`;
        timelineObserver.observe(item);
    });
}

// ==================== 平滑滚动 ====================
function initSmoothScroll() {
    const navLinks = document.querySelectorAll('a[href^="#"]');
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const targetId = link.getAttribute('href').substring(1);
            scrollToSection(targetId);
        });
    });
}

function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        const offsetTop = section.offsetTop - 80; // 考虑导航栏高度
        window.scrollTo({
            top: offsetTop,
            behavior: 'smooth'
        });
    }
}

// ==================== 背景文字动画 ====================
function initBackgroundTextAnimation() {
    const backgroundTexts = document.querySelectorAll('.background-text, .background-text-2');
    
    window.addEventListener('scroll', () => {
        const scrollY = window.scrollY;
        
        backgroundTexts.forEach((text, index) => {
            const speed = (index + 1) * 0.5;
            const yPos = -(scrollY * speed);
            text.style.transform = `translateY(${yPos}px) rotate(${index % 2 === 0 ? -15 : 15}deg)`;
        });
    });
}

// ==================== 导航栏滚动效果 ====================
function initNavbarScroll() {
    const navbar = document.querySelector('.navbar');
    if (!navbar) return;
    
    window.addEventListener('scroll', () => {
        if (window.scrollY > 100) {
            navbar.style.background = 'rgba(244, 58, 71, 0.95)';
            navbar.style.backdropFilter = 'blur(20px)';
        } else {
            navbar.style.background = 'rgba(244, 58, 71, 0.9)';
            navbar.style.backdropFilter = 'blur(10px)';
        }
    });
}

// ==================== 页面加载完成后的额外初始化 ====================
window.addEventListener('load', () => {
    initBackgroundTextAnimation();
    initNavbarScroll();
});

// ==================== 工具函数 ====================
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 防抖处理窗口大小变化
window.addEventListener('resize', debounce(() => {
    // 重新计算布局相关的内容
    console.log('Window resized');
}, 250));

// ==================== X OBERON 水印系统 ====================
function initWatermarkSystem() {
    const watermarkTexts = document.querySelectorAll('.watermark-text');

    // 鼠标移动时的水印响应
    function handleWatermarkMouseMove(e) {
        const mouseX = e.clientX / window.innerWidth;
        const mouseY = e.clientY / window.innerHeight;

        watermarkTexts.forEach((text, index) => {
            const intensity = (index + 1) * 0.3;
            const offsetX = (mouseX - 0.5) * 15 * intensity;
            const offsetY = (mouseY - 0.5) * 8 * intensity;

            // 获取当前的transform值并添加鼠标偏移
            const currentTransform = getComputedStyle(text).transform;
            text.style.transform = currentTransform + ` translate(${offsetX}px, ${offsetY}px)`;
        });
    }

    // 事件监听
    document.addEventListener('mousemove', debounce(handleWatermarkMouseMove, 16)); // 60fps
}

// ==================== 滚动驱动动画系统 ====================
function initScrollDrivenAnimations() {
    if (typeof gsap === 'undefined' || typeof ScrollTrigger === 'undefined') {
        console.warn('GSAP or ScrollTrigger not loaded, using fallback animations');
        initFallbackScrollAnimations();
        return;
    }

    // 水印文字滚动视差效果
    const watermarkTexts = document.querySelectorAll('.watermark-text');
    watermarkTexts.forEach((text, index) => {
        gsap.to(text, {
            y: -50 * (index + 1),
            rotation: (index % 2 === 0 ? 3 : -3),
            opacity: 0.12,
            scrollTrigger: {
                trigger: text,
                start: "top bottom",
                end: "bottom top",
                scrub: 1,
            }
        });
    });

    // 主标题滚动效果 - 更强烈的视觉冲击
    const titleLines = document.querySelectorAll('.title-line');
    titleLines.forEach((line, index) => {
        gsap.fromTo(line,
            {
                y: 150,
                opacity: 0,
                scale: 0.7,
                rotationX: 45
            },
            {
                y: 0,
                opacity: 1,
                scale: 1,
                rotationX: 0,
                duration: 1.2,
                delay: index * 0.3,
                ease: "power3.out",
                scrollTrigger: {
                    trigger: line,
                    start: "top 80%",
                    toggleActions: "play none none reverse"
                }
            }
        );
    });

    // 页面切换时的水印变化 - 更戏剧性的效果
    ScrollTrigger.create({
        trigger: ".projects-section",
        start: "top center",
        end: "bottom center",
        onEnter: () => {
            // 进入项目区域时，水印发生戏剧性变化
            gsap.to('.watermark-text', {
                opacity: 0.02,
                scale: 1.1,
                rotation: "+=5",
                duration: 1.2,
                ease: "power2.out",
                stagger: 0.1
            });
        },
        onLeave: () => {
            // 离开项目区域时，恢复水印
            gsap.to('.watermark-text', {
                opacity: 0.08,
                scale: 1,
                rotation: "-=5",
                duration: 1.2,
                ease: "power2.out",
                stagger: 0.1
            });
        },
        onEnterBack: () => {
            gsap.to('.watermark-text', {
                opacity: 0.02,
                scale: 1.1,
                rotation: "+=5",
                duration: 1.2,
                ease: "power2.out",
                stagger: 0.1
            });
        },
        onLeaveBack: () => {
            gsap.to('.watermark-text', {
                opacity: 0.08,
                scale: 1,
                rotation: "-=5",
                duration: 1.2,
                ease: "power2.out",
                stagger: 0.1
            });
        }
    });
}

// ==================== 备用滚动动画 ====================
function initFallbackScrollAnimations() {
    let ticking = false;

    function updateScrollAnimations() {
        const scrollY = window.scrollY;
        const windowHeight = window.innerHeight;

        // 水印视差效果
        const watermarkTexts = document.querySelectorAll('.watermark-text');
        watermarkTexts.forEach((text, index) => {
            const speed = 0.3 + (index * 0.1);
            const yPos = -(scrollY * speed);
            const rotation = scrollY * 0.01 * (index % 2 === 0 ? 1 : -1);

            text.style.transform = `translateY(${yPos}px) rotate(${rotation}deg)`;
        });

        ticking = false;
    }

    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(updateScrollAnimations);
            ticking = true;
        }
    }

    window.addEventListener('scroll', requestTick);
}

// ==================== 页面切换函数 ====================
function scrollToNextPage() {
    const projectsSection = document.getElementById('projects');
    if (projectsSection) {
        projectsSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// ==================== 多页面导航系统 (优化版) ====================
let currentPageIndex = 0;
const totalPages = 5;
let isTransitioning = false;

function initMultiPageNavigation() {
    const navDots = document.querySelectorAll('.nav-dot');
    const scrollContainer = document.getElementById('scroll-container');

    // 导航点点击事件
    navDots.forEach((dot, index) => {
        dot.addEventListener('click', () => {
            if (!isTransitioning) {
                navigateToPage(index);
            }
        });
    });

    // 滚动监听 - 使用防抖优化
    if (scrollContainer) {
        scrollContainer.addEventListener('scroll', debounce(handlePageScroll, 50));
    }

    // 键盘导航
    document.addEventListener('keydown', (e) => {
        if (isTransitioning) return;

        switch(e.key) {
            case 'ArrowDown':
            case ' ':
                e.preventDefault();
                if (currentPageIndex < totalPages - 1) {
                    navigateToPage(currentPageIndex + 1);
                }
                break;
            case 'ArrowUp':
                e.preventDefault();
                if (currentPageIndex > 0) {
                    navigateToPage(currentPageIndex - 1);
                }
                break;
        }
    });

    // 鼠标滚轮导航 - 优化版
    let wheelTimeout;
    let wheelDelta = 0;

    scrollContainer.addEventListener('wheel', (e) => {
        e.preventDefault(); // 阻止默认滚动

        if (isTransitioning) return;

        wheelDelta += e.deltaY;

        clearTimeout(wheelTimeout);
        wheelTimeout = setTimeout(() => {
            if (Math.abs(wheelDelta) > 50) { // 滚动阈值
                if (wheelDelta > 0 && currentPageIndex < totalPages - 1) {
                    navigateToPage(currentPageIndex + 1);
                } else if (wheelDelta < 0 && currentPageIndex > 0) {
                    navigateToPage(currentPageIndex - 1);
                }
            }
            wheelDelta = 0;
        }, 50);
    }, { passive: false });

    // 初始化第一页
    updateNavigationDots(0);
    triggerPageAnimations(0);
}

function navigateToPage(pageIndex) {
    if (pageIndex < 0 || pageIndex >= totalPages || isTransitioning) return;

    isTransitioning = true;
    const scrollContainer = document.getElementById('scroll-container');
    const targetPage = document.getElementById(`page${pageIndex + 1}`);
    const allPages = document.querySelectorAll('.page-section');

    if (targetPage && scrollContainer) {
        // 立即更新页面状态
        allPages.forEach(page => page.classList.remove('active-section'));
        targetPage.classList.add('active-section');

        currentPageIndex = pageIndex;
        updateNavigationDots(pageIndex);

        // 平滑滚动到目标页面
        scrollContainer.scrollTo({
            top: targetPage.offsetTop,
            behavior: 'smooth'
        });

        // 触发页面动画
        requestAnimationFrame(() => {
            triggerPageAnimations(pageIndex);
        });

        // 较短的过渡时间
        setTimeout(() => {
            isTransitioning = false;
        }, 500);
    } else {
        isTransitioning = false;
    }
}

function handlePageScroll() {
    if (isTransitioning) return;

    const scrollContainer = document.getElementById('scroll-container');
    const sections = document.querySelectorAll('.page-section');

    if (!scrollContainer || !sections.length) return;

    const scrollPosition = scrollContainer.scrollTop;
    const windowHeight = window.innerHeight;

    // 简化的页面检测 - 基于滚动位置
    const newPageIndex = Math.round(scrollPosition / windowHeight);
    const clampedPageIndex = Math.max(0, Math.min(newPageIndex, totalPages - 1));

    // 只有当页面真正改变时才更新
    if (clampedPageIndex !== currentPageIndex) {
        // 移除所有active状态
        sections.forEach(s => s.classList.remove('active-section'));

        // 添加新的active状态
        if (sections[clampedPageIndex]) {
            sections[clampedPageIndex].classList.add('active-section');
        }

        currentPageIndex = clampedPageIndex;
        updateNavigationDots(clampedPageIndex);

        // 使用requestAnimationFrame优化动画触发
        requestAnimationFrame(() => {
            triggerPageAnimations(clampedPageIndex);
        });
    }

    // 更新滚动提示的显示
    const scrollHint = document.querySelector('.scroll-hint');
    if (scrollHint) {
        scrollHint.style.opacity = currentPageIndex === totalPages - 1 ? '0' : '0.7';
    }
}

function updateNavigationDots(activeIndex) {
    const navDots = document.querySelectorAll('.nav-dot');
    navDots.forEach((dot, index) => {
        if (index === activeIndex) {
            dot.classList.add('active');
        } else {
            dot.classList.remove('active');
        }
    });
}

function triggerPageAnimations(pageIndex) {
    // 重置所有页面的动画状态
    resetPageAnimations();

    switch(pageIndex) {
        case 0:
            animateHomePage();
            break;
        case 1:
            animateAboutPage();
            break;
        case 2:
            animateIdentityPage();
            break;
        case 3:
            animateCreatorPage();
            break;
        case 4:
            animateDeveloperPage();
            break;
    }
}

function resetPageAnimations() {
    // 重置所有可能的动画元素
    const animatedElements = document.querySelectorAll(
        '.title-line, .hero-subtitle, .hero-cta, .about-title, .about-description, .identity-title, .whoami-title, .whoami-title-right'
    );

    animatedElements.forEach(el => {
        el.style.transition = 'none';
        el.style.transform = '';
        el.style.opacity = '';
    });
}

function animateHomePage() {
    const titleLines = document.querySelectorAll('.title-line');
    const subtitle = document.querySelector('.hero-subtitle');
    const cta = document.querySelector('.hero-cta');

    titleLines.forEach((line, index) => {
        setTimeout(() => {
            line.style.transition = 'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
            line.style.transform = 'translate3d(0, 0, 0)';
            line.style.opacity = '1';
        }, index * 150);
    });

    if (subtitle) {
        setTimeout(() => {
            subtitle.style.transition = 'all 0.5s ease-out';
            subtitle.style.transform = 'translate3d(0, 0, 0)';
            subtitle.style.opacity = '1';
        }, 450);
    }

    if (cta) {
        setTimeout(() => {
            cta.style.transition = 'all 0.5s ease-out';
            cta.style.transform = 'translate3d(0, 0, 0)';
            cta.style.opacity = '1';
        }, 600);
    }
}

function animateAboutPage() {
    const title = document.querySelector('.about-title');
    const lines = document.querySelectorAll('.about-line, .about-paragraph');

    if (title) {
        setTimeout(() => {
            title.style.transition = 'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
            title.style.transform = 'translate3d(0, 0, 0)';
            title.style.opacity = '1';
        }, 100);
    }

    lines.forEach((el, index) => {
        if (el) {
            setTimeout(() => {
                el.style.transition = 'all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
                el.style.transform = 'translate3d(0, 0, 0)';
                el.style.opacity = '1';
            }, 250 + (index * 150));
        }
    });
}

function animateIdentityPage() {
    const titles = document.querySelectorAll('.identity-title');
    titles.forEach((title, index) => {
        setTimeout(() => {
            title.style.transition = 'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
            title.style.transform = 'translate3d(0, 0, 0) scale(1)';
            title.style.opacity = '1';
        }, index * 200);
    });
}

function animateCreatorPage() {
    // 启动粒子效果
    initParticleEffect();

    const title = document.querySelector('.whoami-title');
    const quote = document.querySelector('.page-quote');
    const link = document.querySelector('.page-link');

    if (title) {
        setTimeout(() => {
            title.style.transition = 'all 1s cubic-bezier(0.4, 0, 0.2, 1)';
            title.style.transform = 'translateY(0)';
            title.style.opacity = '1';
        }, 200);
    }

    if (link) {
        setTimeout(() => {
            link.style.transition = 'all 0.8s ease-out';
            link.style.transform = 'translateY(0)';
            link.style.opacity = '1';
        }, 400);
    }

    if (quote) {
        setTimeout(() => {
            quote.style.transition = 'all 1.2s cubic-bezier(0.4, 0, 0.2, 1)';
            quote.style.transform = 'translateX(0) scale(1)';
            quote.style.opacity = '1';
        }, 600);
    }
}

function animateDeveloperPage() {
    // 启动代码雨效果
    initCodeRain();

    const title = document.querySelector('.whoami-title-right');
    const quote = document.querySelector('.page-quote-right');
    const link = document.querySelector('.page-link-right');

    if (title) {
        setTimeout(() => {
            title.style.transition = 'all 1s cubic-bezier(0.4, 0, 0.2, 1)';
            title.style.transform = 'translateY(0)';
            title.style.opacity = '1';
        }, 200);
    }

    if (link) {
        setTimeout(() => {
            link.style.transition = 'all 0.8s ease-out';
            link.style.transform = 'translateY(0)';
            link.style.opacity = '1';
        }, 400);
    }

    if (quote) {
        setTimeout(() => {
            quote.style.transition = 'all 1.2s cubic-bezier(0.4, 0, 0.2, 1)';
            quote.style.transform = 'translateX(0) scale(1)';
            quote.style.opacity = '1';
        }, 600);
    }
}

// 简单的粒子效果
function initParticleEffect() {
    const container = document.getElementById('particles-container');
    if (!container) return;

    // 清除现有粒子
    container.innerHTML = '';

    for (let i = 0; i < 20; i++) {
        const particle = document.createElement('div');
        particle.style.position = 'absolute';
        particle.style.width = '4px';
        particle.style.height = '4px';
        particle.style.backgroundColor = '#f43a47';
        particle.style.borderRadius = '50%';
        particle.style.opacity = Math.random() * 0.5 + 0.2;
        particle.style.left = Math.random() * 100 + '%';
        particle.style.top = Math.random() * 100 + '%';
        particle.style.animation = `particleFloat ${3 + Math.random() * 4}s ease-in-out infinite`;
        particle.style.animationDelay = Math.random() * 2 + 's';

        container.appendChild(particle);
    }
}

// 简单的代码雨效果
function initCodeRain() {
    const canvas = document.getElementById('code-canvas');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';
    const charArray = chars.split('');

    const fontSize = 14;
    const columns = canvas.width / fontSize;
    const drops = [];

    for (let i = 0; i < columns; i++) {
        drops[i] = 1;
    }

    function draw() {
        ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        ctx.fillStyle = '#f43a47';
        ctx.font = fontSize + 'px monospace';

        for (let i = 0; i < drops.length; i++) {
            const text = charArray[Math.floor(Math.random() * charArray.length)];
            ctx.fillText(text, i * fontSize, drops[i] * fontSize);

            if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
                drops[i] = 0;
            }
            drops[i]++;
        }
    }

    setInterval(draw, 50);
}

// 在初始化时调用多页面导航
document.addEventListener('DOMContentLoaded', () => {
    initMultiPageNavigation();
});
